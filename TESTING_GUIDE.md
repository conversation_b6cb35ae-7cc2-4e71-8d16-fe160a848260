# realtime_chat_v1 更新测试指南

## 快速测试步骤

### 1. 启动应用
```bash
cd /Users/<USER>/Documents/code/realtime_speech_search_agent
python app.py
```

### 2. 访问界面
在浏览器中访问：`https://localhost:7878`

### 3. 测试语音功能

#### 3.1 连接测试
1. 点击绿色的电话按钮 📞
2. 允许浏览器访问麦克风权限
3. 观察按钮变为红色挂断按钮，状态显示"Connecting..."
4. 连接成功后会显示通话时间

#### 3.2 语音交互测试
1. 对着麦克风说话，例如：
   - "你好"
   - "帮我搜索手机"
   - "今天天气怎么样"
   - "介绍一下北京"

2. 观察以下功能：
   - 用户语音转录显示在聊天界面右侧（蓝色气泡）
   - AI回复显示在聊天界面左侧（白色气泡）
   - 语音合成播放AI回复
   - 商品搜索结果显示（如果搜索商品）

#### 3.3 语音打断测试
1. 等待AI开始回复时
2. 立即说话打断AI
3. 观察AI停止说话，开始处理新的语音输入

### 4. 验证更新内容

#### 4.1 前端界面更新 ✅
- [ ] 现代化的渐变背景设计
- [ ] 圆角聊天容器和消息气泡
- [ ] 语音可视化效果（音频环形指示器）
- [ ] 通话状态和计时器显示

#### 4.2 语音交互优化 ✅
- [ ] 语音转录准确性提升
- [ ] 打断逻辑响应更快
- [ ] TTS语音质量改善

#### 4.3 系统提示优化 ✅
- [ ] AI回复更自然、简洁
- [ ] 情感识别和回应
- [ ] 安全性和事实准确性

#### 4.4 技术功能 ✅
- [ ] WebRTC连接稳定
- [ ] 音频流处理正常
- [ ] 商品搜索功能完整
- [ ] 网络搜索功能正常

## 常见问题排查

### 问题1：无法连接WebRTC
**症状**：点击电话按钮后一直显示"Connecting..."
**解决方案**：
1. 检查浏览器是否允许麦克风权限
2. 确认使用HTTPS访问（localhost除外）
3. 检查网络连接
4. 查看浏览器控制台错误信息

### 问题2：没有语音输出
**症状**：能看到文字回复，但听不到语音
**解决方案**：
1. 检查浏览器音频权限
2. 确认系统音量设置
3. 检查音频输出设备
4. 查看控制台是否有音频播放错误

### 问题3：语音识别不准确
**症状**：说话后转录文字不正确
**解决方案**：
1. 确保麦克风质量良好
2. 减少环境噪音
3. 说话清晰、语速适中
4. 检查麦克风权限设置

### 问题4：商品搜索无结果
**症状**：搜索商品时没有显示结果
**解决方案**：
1. 检查网络连接
2. 确认API密钥配置正确
3. 查看服务器日志错误信息
4. 尝试不同的搜索关键词

## 性能监控

### 服务器日志监控
观察终端输出，正常情况下应该看到：
```
INFO: 127.0.0.1:xxxxx - "POST /webrtc/offer HTTP/1.1" 200 OK
INFO: 127.0.0.1:xxxxx - "GET /outputs?webrtc_id=xxxxx HTTP/1.1" 200 OK
```

### 浏览器控制台监控
正常情况下应该看到：
```
Voice chat interface loaded
Setting up WebRTC connection...
WebRTC setup completed
Connection state: connected
```

## 测试用例

### 基础对话测试
1. "你好" → 应该得到友好的问候回复
2. "谢谢" → 应该得到礼貌的回应
3. "再见" → 应该得到告别回复

### 商品搜索测试
1. "帮我找手机" → 应该显示手机商品列表
2. "搜索苹果手机" → 应该显示苹果手机相关商品
3. "1000元以下的耳机" → 应该显示价格筛选后的耳机

### 信息查询测试
1. "今天天气怎么样" → 应该提示需要位置信息或进行网络搜索
2. "介绍一下北京" → 应该得到北京的基本介绍
3. "什么是人工智能" → 应该得到AI相关解释

### 语音打断测试
1. 问一个复杂问题让AI开始长回复
2. 在AI回复过程中立即说话
3. 验证AI停止回复并处理新输入

## 成功标准

✅ **基本功能**：语音输入输出正常工作
✅ **界面更新**：新的UI设计正确显示
✅ **交互优化**：打断逻辑响应迅速
✅ **商品搜索**：搜索功能完整保留
✅ **稳定性**：长时间使用无明显问题

如果以上测试都通过，说明 realtime_chat_v1 更新成功应用！
