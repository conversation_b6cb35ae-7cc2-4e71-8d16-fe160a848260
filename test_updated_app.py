#!/usr/bin/env python3
"""
测试更新后的应用
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有导入是否正常"""
    try:
        print("🔍 测试导入...")
        
        # 测试基础导入
        import app
        print("✅ app.py 导入成功")
        
        # 测试新增的模块
        import system_prompt
        print("✅ system_prompt.py 导入成功")
        
        import msg_type
        print("✅ msg_type.py 导入成功")
        
        # 测试realtime_chat_v1模块
        sys.path.append(str(project_root / "thirdparty" / "realtime_chat_v1"))
        
        from tts8 import ByteDanceTTSStreaming, StreamConfig, TriggerStrategy
        print("✅ tts8 导入成功")
        
        from chat import InterruptibleAzureOpenAI
        print("✅ chat 导入成功")
        
        print("🎉 所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_configuration():
    """测试配置是否正确"""
    try:
        print("\n🔧 测试配置...")
        
        # 检查前端文件
        index_file = project_root / "index2.html"
        if index_file.exists():
            print("✅ index2.html 文件存在")
        else:
            print("❌ index2.html 文件不存在")
            return False
        
        # 检查系统提示
        from system_prompt import system_prompt
        if system_prompt and len(system_prompt) > 100:
            print("✅ system_prompt 配置正确")
        else:
            print("❌ system_prompt 配置有问题")
            return False
        
        print("🎉 配置测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_class_initialization():
    """测试类初始化"""
    try:
        print("\n🏗️ 测试类初始化...")
        
        # 测试音频处理器初始化
        from app import SearchIntegratedAudioHandler
        handler = SearchIntegratedAudioHandler()
        print("✅ SearchIntegratedAudioHandler 初始化成功")
        
        # 测试TTS配置 (stream_config在start_up方法中设置)
        if hasattr(handler, 'tts_server'):
            print("✅ tts_server 配置存在")
        else:
            print("❌ tts_server 配置缺失")
            return False
        
        print("🎉 类初始化测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 类初始化失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 realtime_chat_v1 更新...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_configuration,
        test_class_initialization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            break
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！realtime_chat_v1 更新成功应用！")
        print("\n📝 更新内容总结:")
        print("  ✅ 前端页面更新 (index2.html)")
        print("  ✅ 语音交互打断逻辑优化")
        print("  ✅ system_prompt 更加完善")
        print("  ✅ TTS 功能优化")
        print("  ✅ 消息类型定义优化")
        print("  ✅ 聊天功能增强")
        print("\n🚀 可以启动应用进行测试:")
        print("  python app.py")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
