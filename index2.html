<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time Voice Chat</title>
    <style>
        body {
            font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            height: 100vh;
            box-sizing: border-box;
            overflow: hidden;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            height: calc(100% - 80px);
        }
        .logo {
            text-align: center;
            margin-bottom: 16px;
        }
        .logo h1 {
            font-size: 22px;
            font-weight: 500;
            letter-spacing: 0.3px;
            margin: 0;
            color: #ffffff;
        }
        .phone-icon {
            font-size: 36px;
            margin-bottom: 6px;
            filter: grayscale(0.2);
        }
        .chat-container {
            border: none;
            border-radius: 20px;
            padding: 0;
            height: 90%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            background: #ffffff;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        .chat-messages {
            flex-grow: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 20px 16px;
            background: #f8f9fa;
            border-radius: 20px 20px 0 0;
            scroll-behavior: smooth;
        }
        .chat-messages:empty::before {
            content: "Start a call to begin the conversation";
            display: block;
            text-align: center;
            color: #9ca3af;
            font-size: 14px;
            margin-top: 40px;
        }
        .message-wrapper {
            display: flex;
            margin-bottom: 20px;
            align-items: flex-end;
        }
        .message-wrapper.user {
            justify-content: flex-end;
        }
        .message-wrapper.assistant {
            justify-content: flex-start;
        }
        .message {
            max-width: 75%;
            padding: 12px 18px;
            font-size: 16px;
            line-height: 1.5;
            word-wrap: break-word;
            position: relative;
        }
        .message.user {
            background-color: #4169ff;
            color: white;
            border-radius: 24px 24px 4px 24px;
            margin-left: auto;
            box-shadow: 0 2px 8px rgba(65, 105, 255, 0.3);
        }
        .message.assistant {
            background-color: #ffffff;
            color: #2c3e50;
            border-radius: 24px 24px 24px 4px;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }
        /* Message animation */
        .message-wrapper {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        /* Typography adjustments */
        .message {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            font-weight: 400;
            letter-spacing: 0.01em;
        }
        .message.assistant {
            font-weight: 400;
            color: #2c3e50;
        }
        /* Chat input area placeholder */
        .chat-input-area {
            padding: 16px;
            background: white;
            border-top: 1px solid #e9ecef;
            border-radius: 0 0 20px 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 14px;
        }
        .controls {
            text-align: center;
            margin-top: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .phone-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            position: relative;
            overflow: visible;
        }
        .phone-button.call {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.4);
        }
        .phone-button.call:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 30px rgba(76, 175, 80, 0.6);
        }
        .phone-button.call:active {
            transform: scale(0.95);
        }
        .phone-button.hangup {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            box-shadow: 0 4px 20px rgba(244, 67, 54, 0.4);
        }
        .phone-button.hangup:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 30px rgba(244, 67, 54, 0.6);
        }
        .phone-button.hangup:active {
            transform: scale(0.95);
        }
        .phone-button.connecting {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            animation: pulse 1.5s infinite;
            cursor: not-allowed;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.7);
            }
            70% {
                box-shadow: 0 0 0 20px rgba(255, 152, 0, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
            }
        }
        .call-status {
            margin-top: 10px;
            font-size: 14px;
            text-align: center;
            opacity: 0.8;
            min-height: 10px;
        }
        .call-timer {
            font-size: 18px;
            font-weight: 300;
            letter-spacing: 1px;
            color: #4CAF50;
        }
        #audio-output {
            display: none;
        }
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 0.6s ease-out;
        }
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        /* Audio level indicator */
        .audio-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            pointer-events: none;
            transition: transform 0.1s ease;
        }
        .audio-indicator.active {
            transform: translate(-50%, -50%) scale(var(--audio-level, 1));
        }
        /* Microphone audio visualization */
        .mic-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
            z-index: 2;
        }
        .mic-icon {
            font-size: 24px;
            color: white;
            position: relative;
            z-index: 3;
        }
        .audio-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.6);
            opacity: 0;
            pointer-events: none;
        }
        .audio-ring-1 {
            width: 60px;
            height: 60px;
            animation: audioRing1 2s ease-out infinite;
        }
        .audio-ring-2 {
            width: 80px;
            height: 80px;
            animation: audioRing2 2s ease-out infinite;
            animation-delay: 0.3s;
        }
        .audio-ring-3 {
            width: 100px;
            height: 100px;
            animation: audioRing3 2s ease-out infinite;
            animation-delay: 0.6s;
        }
        @keyframes audioRing1 {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            20% {
                opacity: calc(var(--audio-level, 0) * 0.8);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(1.2);
            }
        }
        @keyframes audioRing2 {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            20% {
                opacity: calc(var(--audio-level, 0) * 0.6);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(1.4);
            }
        }
        @keyframes audioRing3 {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            20% {
                opacity: calc(var(--audio-level, 0) * 0.4);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(1.6);
            }
        }

        /* Disable animations if user prefers reduced motion */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        .phone-button.hangup .audio-ring {
            animation-duration: 1.5s;
        }
        .mic-container {
            display: none;
        }
        .phone-button.hangup .mic-container {
            display: block;
        }
        .phone-button.hangup #phone-icon {
            display: none;
        }
        .hangup-icon {
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 20px;
            display: none;
            z-index: 4;
        }
        .phone-button.hangup .hangup-icon {
            display: block;
        }
        /* Toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 16px 24px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        .toast.error {
            background-color: rgba(244, 67, 54, 0.9);
            color: white;
        }
        .toast.warning {
            background-color: rgba(255, 152, 0, 0.9);
            color: white;
        }
        .toast.info {
            background-color: rgba(33, 150, 243, 0.9);
            color: white;
        }
        /* Audio status */
        .audio-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
            display: none;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        .audio-status.good {
            background-color: rgba(76, 175, 80, 0.9);
            color: white;
        }
        .audio-status.poor {
            background-color: rgba(255, 152, 0, 0.9);
            color: white;
        }
        .audio-status.bad {
            background-color: rgba(244, 67, 54, 0.9);
            color: white;
        }
        /* Scrollbar styling */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }
        .chat-messages::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }
        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.15);
            border-radius: 3px;
        }
        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.25);
        }

        /* Fix for edge flickering */
        * {
            -webkit-backface-visibility: hidden;
            -moz-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        /* Prevent layout shifts */
        .chat-container {
            transform: translateZ(0);
            will-change: transform;
        }

        /* Voice activity indicator */
        .voice-indicator {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 8px 20px;
            background-color: rgba(255, 152, 0, 0.9);
            color: white;
            border-radius: 20px;
            font-size: 12px;
            display: none;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        .voice-indicator.active {
            display: flex;
        }
        .voice-indicator::before {
            content: "🎤";
            font-size: 16px;
            animation: pulse 1s infinite;
        }
    </style>
</head>

<body>
    <!-- Toast notifications -->
    <div id="error-toast" class="toast"></div>
    <div id="audio-status" class="audio-status"></div>
    <div id="voice-indicator" class="voice-indicator">Speaking detected - Audio paused</div>
    
    <div class="container">
        <div class="logo">
            <h1>Voice Chat</h1>
        </div>
        <div class="chat-container">
            <div class="chat-messages" id="chat-messages"></div>
        </div>
        <div class="controls">
            <button id="phone-button" class="phone-button call">
                <span id="phone-icon">📞</span>
                <span class="hangup-icon">❌</span>
                <div class="audio-indicator" id="audio-indicator"></div>
                <div class="mic-container" id="mic-container">
                    <div class="audio-ring audio-ring-1"></div>
                    <div class="audio-ring audio-ring-2"></div>
                    <div class="audio-ring audio-ring-3"></div>
                    <span class="mic-icon">🎤</span>
                </div>
            </button>
            <div class="call-status" id="call-status"></div>
        </div>
    </div>
    <audio id="audio-output"></audio>

    <script>
        let peerConnection;
        let webrtc_id;
        const audioOutput = document.getElementById('audio-output');
        const phoneButton = document.getElementById('phone-button');
        const phoneIcon = document.getElementById('phone-icon');
        const callStatus = document.getElementById('call-status');
        const chatMessages = document.getElementById('chat-messages');
        const audioIndicator = document.getElementById('audio-indicator');
        const voiceIndicator = document.getElementById('voice-indicator');

        let isConnected = false;
        let callStartTime = null;
        let callTimer = null;
        let audioContext = null;
        let analyser = null;
        let microphone = null;
        let dataArray = null;
        let originalVolume = 1.0;
        let isAudioDucked = false;

        // Initialize audio context for visualization
        async function initAudioContext() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;
                dataArray = new Uint8Array(analyser.frequencyBinCount);

                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                microphone = audioContext.createMediaStreamSource(stream);
                microphone.connect(analyser);

                // Start audio level monitoring
                monitorAudioLevel();
            } catch (error) {
                console.error('Error initializing audio context:', error);
            }
        }

        function monitorAudioLevel() {
            if (!analyser || !dataArray) return;

            analyser.getByteFrequencyData(dataArray);
            const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
            const audioLevel = Math.min(average / 128, 1);

            // Update CSS custom property for audio level
            document.documentElement.style.setProperty('--audio-level', audioLevel);

            // Update audio indicator
            if (audioLevel > 0.1) {
                audioIndicator.classList.add('active');
            } else {
                audioIndicator.classList.remove('active');
            }

            requestAnimationFrame(monitorAudioLevel);
        }

        function updateCallTimer() {
            if (!callStartTime) return;

            const elapsed = Math.floor((Date.now() - callStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            callStatus.innerHTML = `<span class="call-timer">${timeString}</span>`;
        }

        function startCallTimer() {
            callStartTime = Date.now();
            callTimer = setInterval(updateCallTimer, 1000);
            updateCallTimer();
        }

        function stopCallTimer() {
            if (callTimer) {
                clearInterval(callTimer);
                callTimer = null;
            }
            callStartTime = null;
            callStatus.textContent = '';
        }

        function addMessage(content, role) {
            const messageWrapper = document.createElement('div');
            messageWrapper.className = `message-wrapper ${role}`;

            const message = document.createElement('div');
            message.className = `message ${role}`;
            message.textContent = content;

            messageWrapper.appendChild(message);
            chatMessages.appendChild(messageWrapper);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showToast(message, type = 'info') {
            const toast = document.getElementById('error-toast');
            toast.textContent = message;
            toast.className = `toast ${type}`;
            toast.style.display = 'block';

            setTimeout(() => {
                toast.style.display = 'none';
            }, 3000);
        }

        function updateAudioStatus(status) {
            const audioStatus = document.getElementById('audio-status');
            audioStatus.textContent = status;
            audioStatus.className = 'audio-status good';
            audioStatus.style.display = 'block';

            setTimeout(() => {
                audioStatus.style.display = 'none';
            }, 2000);
        }

        function duckAudio() {
            if (!isAudioDucked && audioOutput.volume > 0) {
                originalVolume = audioOutput.volume;
                audioOutput.volume = originalVolume * 0.3; // Reduce to 30%
                isAudioDucked = true;
                voiceIndicator.classList.add('active');
                console.log('Audio ducked to 30%');
            }
        }

        function restoreAudio() {
            if (isAudioDucked) {
                audioOutput.volume = originalVolume;
                isAudioDucked = false;
                voiceIndicator.classList.remove('active');
                console.log('Audio restored to original volume');
            }
        }

        async function startCall() {
            try {
                phoneButton.className = 'phone-button connecting';
                callStatus.textContent = 'Connecting...';

                // Initialize audio context
                await initAudioContext();

                const response = await fetch('/start_call', { method: 'POST' });
                const data = await response.json();
                webrtc_id = data.webrtc_id;

                // Setup WebRTC connection
                await setupWebRTC();

                // Start listening for outputs
                listenForOutputs();

                isConnected = true;
                phoneButton.className = 'phone-button hangup';
                startCallTimer();

                addMessage('Call connected', 'assistant');
                updateAudioStatus('Connected');

            } catch (error) {
                console.error('Error starting call:', error);
                showToast('Failed to start call', 'error');
                phoneButton.className = 'phone-button call';
                callStatus.textContent = '';
            }
        }

        async function endCall() {
            try {
                isConnected = false;
                phoneButton.className = 'phone-button call';
                stopCallTimer();

                if (peerConnection) {
                    peerConnection.close();
                    peerConnection = null;
                }

                if (audioContext) {
                    audioContext.close();
                    audioContext = null;
                }

                addMessage('Call ended', 'assistant');

            } catch (error) {
                console.error('Error ending call:', error);
            }
        }

        async function setupWebRTC() {
            // WebRTC setup code would go here
            // This is a placeholder for the actual WebRTC implementation
            console.log('Setting up WebRTC connection...');
        }

        function listenForOutputs() {
            if (!webrtc_id) return;

            const eventSource = new EventSource(`/outputs?webrtc_id=${webrtc_id}`);

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);

                    if (data.type === 'volume_control') {
                        if (data.action === 'reduce') {
                            duckAudio();
                        } else if (data.action === 'restore') {
                            restoreAudio();
                        }
                    } else if (data.content && data.content.includes('[user]:')) {
                        const userMessage = data.content.replace('[user]:', '');
                        addMessage(userMessage, 'user');
                    } else if (data.content && !data.content.includes('[')) {
                        addMessage(data.content, 'assistant');
                    }
                } catch (error) {
                    console.error('Error parsing output:', error);
                }
            };

            eventSource.onerror = function(error) {
                console.error('EventSource error:', error);
                if (!isConnected) {
                    eventSource.close();
                }
            };
        }

        // Event listeners
        phoneButton.addEventListener('click', function() {
            if (isConnected) {
                endCall();
            } else {
                startCall();
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Voice chat interface loaded');
        });
    </script>
</body>
</html>
