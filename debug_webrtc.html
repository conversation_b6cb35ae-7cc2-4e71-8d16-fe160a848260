<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>WebRTC Debug Tool</h1>
    
    <div class="status info">
        <strong>Purpose:</strong> This tool helps debug WebRTC connection issues with FastRTC.
    </div>
    
    <div>
        <button class="btn-primary" onclick="testConnection()">Test WebRTC Connection</button>
        <button class="btn-danger" onclick="clearLog()">Clear Log</button>
    </div>
    
    <h3>Connection Log:</h3>
    <div id="log"></div>
    
    <h3>Current Status:</h3>
    <div id="status" class="status info">Ready to test</div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            // Update status
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            logElement.textContent = '';
            log('Log cleared');
        }
        
        async function testConnection() {
            log('Starting WebRTC connection test...', 'info');
            
            try {
                // Step 1: Test basic fetch to server
                log('Testing server connectivity...');
                const pingResponse = await fetch('/');
                if (pingResponse.ok) {
                    log('✓ Server is reachable', 'success');
                } else {
                    log('✗ Server returned error: ' + pingResponse.status, 'error');
                    return;
                }
                
                // Step 2: Test WebRTC offer endpoint
                log('Testing WebRTC offer endpoint...');
                
                // Create a simple RTCPeerConnection
                const peerConnection = new RTCPeerConnection({
                    iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                });
                
                // Get user media
                log('Requesting microphone access...');
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                log('✓ Microphone access granted', 'success');
                
                // Add track
                stream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, stream);
                });
                
                // Create offer
                log('Creating WebRTC offer...');
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                // Wait for ICE gathering
                log('Gathering ICE candidates...');
                await new Promise((resolve) => {
                    if (peerConnection.iceGatheringState === "complete") {
                        resolve();
                    } else {
                        const checkState = () => {
                            if (peerConnection.iceGatheringState === "complete") {
                                peerConnection.removeEventListener("icegatheringstatechange", checkState);
                                resolve();
                            }
                        };
                        peerConnection.addEventListener("icegatheringstatechange", checkState);
                    }
                });
                log('✓ ICE gathering complete', 'success');
                
                // Generate webrtc_id
                const webrtc_id = Math.random().toString(36).substring(7);
                log(`Generated webrtc_id: ${webrtc_id}`);
                
                // Send offer to server
                log('Sending offer to server...');
                const response = await fetch('/webrtc/offer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sdp: peerConnection.localDescription.sdp,
                        type: peerConnection.localDescription.type,
                        webrtc_id: webrtc_id
                    })
                });
                
                log(`Server response status: ${response.status}`);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`✗ Server error: ${errorText}`, 'error');
                    return;
                }
                
                const serverResponse = await response.json();
                log(`Server response: ${JSON.stringify(serverResponse, null, 2)}`);
                
                if (serverResponse.status === 'failed') {
                    log(`✗ WebRTC setup failed: ${serverResponse.meta?.error}`, 'error');
                    if (serverResponse.meta?.error === 'concurrency_limit_reached') {
                        log(`Concurrency limit: ${serverResponse.meta?.limit}`, 'warning');
                    }
                    return;
                }
                
                // Set remote description
                log('Setting remote description...');
                await peerConnection.setRemoteDescription(serverResponse);
                log('✓ Remote description set', 'success');
                
                // Test outputs endpoint
                log('Testing outputs endpoint...');
                const eventSource = new EventSource(`/outputs?webrtc_id=${webrtc_id}`);
                
                eventSource.onopen = () => {
                    log('✓ EventSource connection opened', 'success');
                    setTimeout(() => {
                        eventSource.close();
                        log('EventSource connection closed (test complete)', 'info');
                    }, 2000);
                };
                
                eventSource.onerror = (error) => {
                    log('✗ EventSource error: ' + error, 'error');
                    eventSource.close();
                };
                
                log('✓ WebRTC connection test completed successfully!', 'success');
                
                // Clean up
                setTimeout(() => {
                    peerConnection.close();
                    stream.getTracks().forEach(track => track.stop());
                    log('Resources cleaned up', 'info');
                }, 3000);
                
            } catch (error) {
                log(`✗ Test failed: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            log('WebRTC Debug Tool loaded');
            log('Click "Test WebRTC Connection" to start debugging');
        });
    </script>
</body>
</html>
